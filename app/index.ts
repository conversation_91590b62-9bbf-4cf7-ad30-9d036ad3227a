import { tap } from '@kdt310722/utils/function'
import { logger } from './core/logger'
import { setJsonRpcRequestHandler, startServer } from './core/server'
import { initializeWorker } from './core/worker'
import 'reflect-metadata'

setJsonRpcRequestHandler(() => {
    return Promise.resolve().then(() => ({ body: { jsonrpc: '2.0', id: null, result: 'just a test' } }))
})

async function main() {
    const timer = tap(logger.createTimer(), () => logger.info('Starting application...'))

    await initializeWorker()
    await startServer()

    logger.stopTimer(timer, 'info', 'Application started!')
}

main().catch((error) => {
    logger.exit(1, 'fatal', 'Failed to start application', error)
})
