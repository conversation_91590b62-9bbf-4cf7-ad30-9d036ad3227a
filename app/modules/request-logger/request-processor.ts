import type { AnyObject } from '@kdt310722/utils/object'
import type { DataSource, Repository } from 'typeorm'
import type { JsonRpcHttpRequestInfo, JsonRpcHttpResponse } from '../jsonrpc-server/types'
import { existsSync } from 'node:fs'
import { mkdir, writeFile } from 'node:fs/promises'
import { join } from 'node:path'
import { notNullish } from '@kdt310722/utils/common'
import { createDeferred, type DeferredPromise } from '@kdt310722/utils/promise'
import Bottleneck from 'bottleneck'
import PQueue from 'p-queue'
import { Request } from '../../entities/request'
import { chunkUpsert } from '../../utils/database/repositories'
import { storagePath } from '../../utils/path'
import { toRequestEntity } from './utils/requests'

export interface RequestProcessorOptions {
    maxBodyLength?: number
    maxConcurrency?: number
    batchSize?: number
    batchTimeout?: number
}

export class RequestProcessor {
    protected readonly maxBodyLength: number
    protected readonly queue: PQueue
    protected readonly batcher: Bottleneck.Batcher
    protected readonly requests: Record<string, Request | undefined> = {}
    protected readonly repository: Repository<Request>
    protected readonly pendingUpdates: Record<string, DeferredPromise<void> | undefined> = {}

    public constructor(database: DataSource, { maxBodyLength = 1000, maxConcurrency = 10, batchSize = maxConcurrency, batchTimeout = 100 }: RequestProcessorOptions = {}) {
        this.repository = database.getRepository(Request)
        this.maxBodyLength = maxBodyLength
        this.queue = new PQueue({ concurrency: maxConcurrency })
        this.batcher = this.createBatcher(batchTimeout, batchSize)
    }

    public add(request: Omit<JsonRpcHttpRequestInfo, 'signal'>) {
        this.requests[request.id] = toRequestEntity(request)
    }

    public error(id: string, error: AnyObject) {
        if (this.requests[id]) {
            this.requests[id].error = error
        }
    }

    public async abort(id: string) {
        await this.queue.add(async () => this.update(id, { isAborted: true }).then(() => delete this.requests[id]))
    }

    public async end(id: string, response?: JsonRpcHttpResponse) {
        await this.queue.add(async () => {
            const request = this.requests[id]

            if (!request) {
                return
            }

            request.body = await this.transform(id, 'request', request.body)

            if (notNullish(response)) {
                const executionTimeHeader = response.headers?.['X-Execution-Time'] ?? response.headers?.['x-execution-time']

                if (executionTimeHeader) {
                    request.executionTime = BigInt(executionTimeHeader)
                }

                request.response = await this.transform(id, 'response', response)
            }

            await this.save(id, request)
        })
    }

    protected async transform<T>(id: string, type: 'request' | 'response', data: T) {
        const serialized = JSON.stringify(data)

        if (serialized.length > this.maxBodyLength) {
            return this.saveToFile(type, id, serialized)
        }

        return serialized as T
    }

    protected async saveToFile(type: 'request' | 'response', id: string, data: string) {
        const directory = storagePath(`${type}s`)
        const path = join(directory, `${Date.now()}-${Math.random().toString(36).slice(2, 10)}-${id}.json`)

        if (!existsSync(directory)) {
            await mkdir(directory, { recursive: true })
        }

        return writeFile(path, data, 'utf8').then(() => ({ path }))
    }

    protected async update(id: string, data: Partial<Request>) {
        if (this.requests[id]) {
            for (const key of Object.keys(data)) {
                if (this.requests[id]) {
                    this.requests[id][key] = data[key]
                }
            }

            await this.save(id, this.requests[id])
        }
    }

    protected async save(requestId: string, request: Request) {
        this.pendingUpdates[requestId] = createDeferred<void>()

        await this.batcher.add({ requestId, entity: request }).catch((error) => this.pendingUpdates[requestId]?.reject(error))
        await this.pendingUpdates[requestId].finally(() => delete this.pendingUpdates[requestId])
    }

    protected createBatcher(maxTime: number, maxSize: number) {
        const batcher = new Bottleneck.Batcher({ maxTime, maxSize })

        batcher.on('batch', async (requests: Array<{ requestId: string, entity: Request }>) => {
            let error: unknown | undefined

            try {
                await chunkUpsert(this.repository, requests.map((i) => i.entity), ['id'])
            } catch (error_) {
                error = error_
            }

            for (const { requestId } of requests) {
                if (error) {
                    this.pendingUpdates[requestId]?.reject(error)
                } else {
                    this.pendingUpdates[requestId]?.resolve()
                }

                delete this.pendingUpdates[requestId]
            }
        })

        return batcher
    }
}
