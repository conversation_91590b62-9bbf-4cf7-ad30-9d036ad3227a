import type { AnyObject } from '@kdt310722/utils/object'
import type { DataSource, Repository } from 'typeorm'
import type { JsonRpcHttpRequestInfo, JsonRpcHttpResponse } from '../jsonrpc-server/types'
import { existsSync } from 'node:fs'
import { mkdir, writeFile } from 'node:fs/promises'
import { join } from 'node:path'
import { notNullish } from '@kdt310722/utils/common'
import { stringifyJson } from '@kdt310722/utils/json'
import PQueue from 'p-queue'
import { Request } from '../../entities/request'
import { storagePath } from '../../utils/path'
import { toRequestEntity } from './utils/requests'

export const MAX_BODY_LENGTH = 1000

export class RequestProcessor {
    protected readonly queue = new PQueue({ concurrency: 1 })
    protected readonly requests: Record<string, Promise<Request> | undefined> = {}
    protected readonly repository: Repository<Request>

    public constructor(database: DataSource) {
        this.repository = database.getRepository(Request)
    }

    public async add(request: Omit<JsonRpcHttpRequestInfo, 'signal'>) {
        await this.queue.add(async () => this.requests[request.id] = this.repository.save(await this.transformRequest(request)))
    }

    public async abort(id: string) {
        await this.queue.add(async () => this.requests[id]?.then(async (request) => this.repository.update(request.id, { isAborted: true })))
    }

    public async end(id: string, response?: JsonRpcHttpResponse) {
        if (notNullish(response)) {
            await this.queue.add(async () => this.requests[id]?.then(async (request) => this.repository.update(request.id, { response: await this.transformResponse(id, response) }))).then(() => {
                delete this.requests[id]
            })
        } else {
            this.requests[id]?.then(() => delete this.requests[id])
        }
    }

    public async error(id: string, error: AnyObject) {
        await this.queue.add(async () => this.requests[id]?.then(async (request) => this.repository.update(request.id, { error })))
    }

    protected async transformResponse(id: string, response: JsonRpcHttpResponse) {
        const serialized = stringifyJson(response)

        if (serialized.length > MAX_BODY_LENGTH) {
            return this.saveToFile('response', id, serialized)
        }

        return serialized as unknown as JsonRpcHttpResponse
    }

    protected async transformRequest(request: Omit<JsonRpcHttpRequestInfo, 'signal'>) {
        const entity = toRequestEntity(request)
        const body = stringifyJson(entity.body)

        if (body.length > MAX_BODY_LENGTH) {
            entity.body = await this.saveToFile('request', request.id, body)
        } else {
            entity.body = body as any
        }

        return entity
    }

    protected async saveToFile(type: 'request' | 'response', id: string, data: string) {
        const directory = storagePath(`${type}s`)
        const path = join(directory, `${Date.now()}-${Math.random().toString(36).slice(2, 10)}-${id}.json`)

        if (!existsSync(directory)) {
            await mkdir(directory, { recursive: true })
        }

        return writeFile(path, data, 'utf8').then(() => ({ path }))
    }
}
